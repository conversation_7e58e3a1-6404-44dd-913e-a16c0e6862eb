<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="jpa" name="JPA">
      <configuration>
        <setting name="validation-enabled" value="true" />
        <setting name="provider-name" value="Hibernate" />
        <datasource-mapping>
          <factory-entry name="college_project_manager" />
          <factory-entry name="entityManagerFactory" />
        </datasource-mapping>
        <naming-strategy-map />
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
</module>