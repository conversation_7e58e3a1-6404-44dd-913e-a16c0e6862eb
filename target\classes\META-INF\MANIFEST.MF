Manifest-Version: 1.0
Class-Path: spring-web-4.3.11.RELEASE.jar stax-api-1.0.1.jar spring-bo
 ot-starter-web-1.5.7.RELEASE.jar springfox-swagger-common-2.6.1.jar h
 ibernate-core-5.1.0.Final.jar slf4j-api-1.7.25.jar springfox-swagger-
 ui-2.6.1.jar springfox-core-2.6.1.jar jandex-2.0.0.Final.jar spring-p
 lugin-metadata-1.2.0.RELEASE.jar commons-collections-3.2.2.jar dom4j-
 1.6.1.jar snakeyaml-1.17.jar springfox-spi-2.6.1.jar spring-session-1
 .3.1.RELEASE.jar shiro-web-1.3.2.jar hibernate-validator-5.1.0.Final.
 jar tomcat-embed-el-8.5.20.jar spring-boot-starter-tomcat-1.5.7.RELEA
 SE.jar jpa-spec-3.1.0.jar poi-3.9.jar shiro-spring-1.3.2.jar jul-to-s
 lf4j-1.7.25.jar spring-aop-4.3.11.RELEASE.jar aspectjweaver-1.8.10.ja
 r jedis-2.9.0.jar javassist-3.21.0-GA.jar spring-boot-starter-data-jp
 a-1.5.7.RELEASE.jar javax.transaction-api-1.2.jar jcl-over-slf4j-1.7.
 25.jar spring-data-jpa-1.11.7.RELEASE.jar springfox-schema-2.6.1.jar 
 commons-codec-1.10.jar spring-oxm-4.3.11.RELEASE.jar guava-18.0.jar h
 ibernate-jpa-2.1-api-1.0.0.Final.jar spring-boot-1.5.7.RELEASE.jar sp
 ring-boot-autoconfigure-1.5.7.RELEASE.jar fastjson-1.2.17.jar poi-oox
 ml-3.9.jar jackson-core-2.8.10.jar spring-expression-4.3.11.RELEASE.j
 ar spring-aspects-4.3.11.RELEASE.jar swagger-models-1.5.10.jar tomcat
 -embed-core-8.5.20.jar classmate-1.3.4.jar spring-boot-starter-1.5.7.
 RELEASE.jar xmlbeans-2.3.0.jar jackson-annotations-2.8.0.jar mockito-
 core-1.10.19.jar shiro-core-1.3.2.jar tomcat-juli-8.5.20.jar mysql-co
 nnector-java-5.1.44.jar poi-scratchpad-3.9.jar commons-beanutils-1.9.
 3.jar spring-boot-starter-data-redis-1.5.7.RELEASE.jar logback-classi
 c-1.1.11.jar spring-data-redis-1.8.7.RELEASE.jar spring-boot-starter-
 aop-1.5.7.RELEASE.jar poi-ooxml-schemas-3.9.jar springfox-swagger2-2.
 6.1.jar spring-webmvc-4.3.11.RELEASE.jar spring-data-keyvalue-1.2.7.R
 ELEASE.jar spring-beans-4.3.11.RELEASE.jar objenesis-2.1.jar spring-j
 dbc-4.3.11.RELEASE.jar xml-apis-1.4.01.jar antlr-2.7.7.jar spring-boo
 t-starter-logging-1.5.7.RELEASE.jar logback-core-1.1.11.jar spring-bo
 ot-starter-jdbc-1.5.7.RELEASE.jar jackson-databind-2.8.10.jar geronim
 o-jta_1.1_spec-1.1.1.jar validation-api-1.1.0.Final.jar log4j-over-sl
 f4j-1.7.25.jar spring-tx-4.3.11.RELEASE.jar jboss-logging-3.3.1.Final
 .jar swagger-annotations-1.5.10.jar tomcat-embed-websocket-8.5.20.jar
  spring-context-support-4.3.11.RELEASE.jar springfox-spring-web-2.6.1
 .jar spring-plugin-core-1.2.0.RELEASE.jar spring-context-4.3.11.RELEA
 SE.jar hibernate-commons-annotations-5.0.1.Final.jar spring-orm-4.3.1
 1.RELEASE.jar mapstruct-1.0.0.Final.jar commons-pool2-2.4.2.jar hamcr
 est-core-1.3.jar tomcat-jdbc-8.5.20.jar hibernate-entitymanager-5.1.0
 .Final.jar spring-core-4.3.11.RELEASE.jar spring-session-data-redis-1
 .3.1.RELEASE.jar spring-data-commons-1.13.7.RELEASE.jar commons-lang3
 -3.5.jar
Main-Class: com.javanoteany.Start

