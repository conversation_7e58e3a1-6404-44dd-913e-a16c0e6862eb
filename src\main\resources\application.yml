server:
  port: 8181
  context-path: /college_project_manager
spring:
  datasource:
      username: root
      password: 123456
      driver-class-name: com.mysql.jdbc.Driver
      url: **************************************************************************************************************************
  application:
    name: college_project_manager_service
  jpa:
    generate-ddl: true
    properties:
      format_sql: true
    hibernate:
      ddl-auto: update
    show-sql: true
    database: mysql
  thymeleaf:
    cache: false
    cache-period: 0
  redis:
    host: localhost
    port: 6379
  session:
    store-type: redis
  aop:
    auto: true
    proxy-target-class: true
logging:
  level:
    com:
      mryao: DEBUG